// app.js

window.addEventListener("DOMContentLoaded", async () => {
  const container = document.getElementById("employee-container");

  try {
    const snapshot = await db
      .collection("employee_of_the_month")
      .orderBy("month", "desc")
      .limit(1)
      .get();

    const employee = snapshot.docs[0].data();

    container.innerHTML = `
      <div class="card">
        <img src="${employee.photo_url}" alt="Employee photo" />
        <h2>${employee.name}</h2>
        <p><strong>Position:</strong> ${employee.position}</p>
        <p><strong>Department:</strong> ${employee.department}</p>
        <p><strong>Month:</strong> ${employee.month}</p>
        <p>${employee.description}</p>
      </div>
    `;
  } catch (error) {
    console.error("Error loading employee:", error);
    container.innerHTML = `<p>Error loading employee of the month.</p>`;
  }
});
