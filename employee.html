<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Employee of the Month</title>
  <link rel="stylesheet" href="style.css">
</head>
<body>
  <div class="container">
    <header>
      <h1>🏆 Employee of the Month</h1>
      <p>Recognizing excellence and dedication in the workplace.</p>
    </header>

    <section class="criteria">
      <h2>Selection Criteria</h2>
      <ul>
        <li>✔️ Punctuality & Attendance</li>
        <li>✔️ Quality of Work</li>
        <li>✔️ Team Collaboration</li>
        <li>✔️ Customer Service & Communication</li>
        <li>✔️ Initiative & Continuous Improvement</li>
      </ul>
    </section>

    <div class="button-container">
      <a href="employee.html" class="button">View This Month’s Employee</a>
    </div>
  </div>
</body>
</html>

<!-- employee.html -->
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Featured Employee</title>
  <link rel="stylesheet" href="style.css">
</head>
<body>
  <div class="container">
    <header>
      <h1>🎉 This Month’s Top Employee</h1>
      <p>Celebrating the outstanding performer of the month.</p>
    </header>

    <section class="employee-card">
      <img src="https://via.placeholder.com/300x300" alt="Employee Photo" class="employee-photo">
      <h2>Fatima AlSayyah</h2>
      <p><strong>Position:</strong> Cybersecurity Analyst</p>
      <p><strong>Department:</strong> IT</p>
      <p><strong>Month:</strong> July 2025</p>
      <p class="description">Demonstrated outstanding leadership, delivered excellent results on all assigned projects, and contributed to team growth and motivation.</p>
    </section>

    <div class="button-container">
      <a href="index.html" class="button">Back to Criteria</a>
    </div>
  </div>
</body>
</html>

/* ---- style.css ---- */

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  margin: 0;
  background-color: #f4f6f8;
  color: #1f2937;
}

.container {
  max-width: 700px;
  margin: auto;
  padding: 2rem;
  background: #fff;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);
  border-radius: 10px;
  margin-top: 3rem;
}

header h1 {
  color: #0f172a;
  margin-bottom: 0.5rem;
}

header p {
  color: #475569;
  font-size: 1rem;
}

.criteria h2 {
  margin-top: 2rem;
  color: #1e293b;
}

.criteria ul {
  list-style: none;
  padding: 0;
}

.criteria li {
  background: #e2e8f0;
  margin-bottom: 0.5rem;
  padding: 0.75rem;
  border-radius: 5px;
  font-size: 1rem;
}

.employee-card {
  text-align: center;
  margin-top: 2rem;
}

.employee-photo {
  width: 300px;
  height: 300px;
  border-radius: 12px;
  object-fit: cover;
  box-shadow: 0 4px 10px rgba(0,0,0,0.1);
  margin-bottom: 1rem;
}

.description {
  margin-top: 1rem;
  color: #334155;
  font-size: 1rem;
}

.button-container {
  text-align: center;
  margin-top: 2rem;
}

.button {
  background-color: #1e40af;
  color: white;
  padding: 0.75rem 1.5rem;
  text-decoration: none;
  border-radius: 5px;
  font-weight: bold;
  transition: background 0.3s;
}

.button:hover {
  background-color: #1c3aa9;
}
